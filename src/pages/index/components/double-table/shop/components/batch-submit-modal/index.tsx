import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Button, Tooltip } from 'antd';
import { BatchSubmitStep, IShopInfo, ITargetShopSetting } from '@/types/batch-submit';
import ShopSelector from './shop-selector';
import SubmitReview from './submit-review';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { queryUnfinishedOptWoosBizOrder } from '@/services/batch-submit';
import { configBusinessNewsGrey } from '@/services';
import { useRequest } from 'ahooks';

const BatchSubmitModal: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState<BatchSubmitStep>(BatchSubmitStep.SHOP_SELECT);
  const [selectedShops, setSelectedShops] = useState<IShopInfo[]>([]);
  const [targetShopSettings, setTargetShopSettings] = useState<ITargetShopSetting[]>([]);
  const [batchBtnDisabled, setBatchBtnDisabled] = useState(false);

  // 查询批量提报灰度开关
  const { data: batchSubmitEnabled } = useRequest(
    async () => {
      const res = await configBusinessNewsGrey(['OPT_ESP_ORDER_BATCH_SUBMIT_GREY']);
      return res?.[0]?.switchStatus;
    },
    {
      onSuccess: (data) => {
        if (data) {
          traceExp(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {});
        }
      },
    },
  );

  // 查询未完结批量提报任务
  useEffect(() => {
    if (batchSubmitEnabled) {
      queryUnfinishedOptWoosBizOrder({}).then((res) => {
        setBatchBtnDisabled(res.result === true);
      });
    }
  }, [batchSubmitEnabled]);

  // 弹窗曝光埋点
  useEffect(() => {
    if (modalVisible) {
      traceExp(PageSPMKey.首页, ModuleSPMKey['批量提报.弹窗曝光'], {});
    }
  }, [modalVisible]);
  // 步骤切换埋点
  useEffect(() => {
    if (modalVisible) {
      if (currentStep === BatchSubmitStep.SHOP_SELECT) {
        traceExp(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择步骤'], {});
      } else if (currentStep === BatchSubmitStep.SUBMIT_REVIEW) {
        traceExp(PageSPMKey.首页, ModuleSPMKey['批量提报.提报审核步骤'], {});
      }
    }
  }, [currentStep, modalVisible]);

  // 打开弹窗
  const handleOpenModal = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {});
    setModalVisible(true);
  }, []);

  // 关闭弹窗时清空所有数据
  const handleClose = useCallback(() => {
    setCurrentStep(BatchSubmitStep.SHOP_SELECT);
    setSelectedShops([]);
    setTargetShopSettings([]);
    setModalVisible(false);
  }, []);

  // 步骤切换
  const handleNext = useCallback(() => {
    setCurrentStep(BatchSubmitStep.SUBMIT_REVIEW);
  }, []);
  const handlePrev = useCallback(() => {
    setCurrentStep(BatchSubmitStep.SHOP_SELECT);
  }, []);

  // 门店选择回调
  const handleShopsSelected = useCallback((shops: IShopInfo[]) => {
    setSelectedShops(shops);
  }, []);

  // 目标门店设置回调
  const handleTargetShopChange = useCallback((settings: ITargetShopSetting[]) => {
    setTargetShopSettings(settings);
  }, []);

  // 提交成功后关闭弹窗
  const handleSubmit = useCallback(() => {
    handleClose();
  }, [handleClose]);

  // 如果灰度开关未开启，不显示批量提报按钮
  if (!batchSubmitEnabled) {
    return null;
  }

  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: 16,
          background: '#fff',
        }}
      >
        <Tooltip title={batchBtnDisabled ? '正在处理中，暂时无法批量提报，请过5分钟后再试。' : ''}>
          <Button type="primary" disabled={batchBtnDisabled} onClick={handleOpenModal}>
            批量提报
          </Button>
        </Tooltip>
      </div>
      <Modal
        open={modalVisible}
        onCancel={handleClose}
        footer={null}
        width={1200}
        destroyOnClose
        title="批量提报"
      >
        <Steps
          current={currentStep - 1}
          items={[{ title: '门店选择' }, { title: '提报审核' }]}
          style={{ marginBottom: 24 }}
        />
        {currentStep === BatchSubmitStep.SHOP_SELECT && (
          <ShopSelector
            onShopsSelected={handleShopsSelected}
            onCancel={handleClose}
            onNext={handleNext}
          />
        )}
        {currentStep === BatchSubmitStep.SUBMIT_REVIEW && (
          <SubmitReview
            shops={selectedShops}
            onTargetShopChange={handleTargetShopChange}
            onCancel={handleClose}
            onPrev={handlePrev}
            onSubmit={handleSubmit}
          />
        )}
      </Modal>
    </>
  );
};

export default BatchSubmitModal;
